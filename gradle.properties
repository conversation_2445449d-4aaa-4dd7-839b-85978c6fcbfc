# IntelliJ Platform Artifacts Repositories -> https://plugins.jetbrains.com/docs/intellij/intellij-artifacts.html
propertiesPluginEnvironmentNameProperty=platformVersion
# Supported platforms: 223, 233, 241
platformVersion=241

pluginGroup = com.phodal.autodev
pluginName = AutoDev
pluginRepositoryUrl = https://github.com/unit-mesh/auto-dev
# SemVer format -> https://semver.org
pluginVersion = 2.2.4

# Supported IDEs: idea, pycharm
baseIDE=idea
pycharmVersion=PC-2023.2.4
golandVersion=GO-2023.3.2
webstormVersion=WS-2022.2.4
riderVersion=RD-2022.2.4

# IntelliJ Platform Properties -> https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html#configuration-intellij-extension

# Gradle Releases -> https://github.com/gradle/gradle/releases
gradleVersion = 8.1

# Opt-out flag for bundling Kotlin standard library -> https://jb.gg/intellij-platform-kotlin-stdlib
kotlin.stdlib.default.dependency = false

# Enable Gradle Configuration Cache -> https://docs.gradle.org/current/userguide/configuration_cache.html
org.gradle.configuration-cache = true

# Enable Gradle Build Cache -> https://docs.gradle.org/current/userguide/build_cache.html
org.gradle.caching = true

# Enable Gradle Kotlin DSL Lazy Property Assignment -> https://docs.gradle.org/current/userguide/kotlin_dsl.html#kotdsl:assignment
systemProp.org.gradle.unsafe.kotlin.assignment = true

# Temporary workaround for Kotlin Compiler OutOfMemoryError -> https://jb.gg/intellij-platform-kotlin-oom
#kotlin.incremental.useClasspathSnapshot = false

javaVersion = 17

kotlin.daemon.jvmargs=-Xmx2g
org.gradle.jvmargs =-Xmx2g

# uncomment to use Intellij Community as local run target
ideaRunVersion=IC-2024.1
# uncomment to use Intellij Ultimate as local run target
#ideaRunVersion=IU-2024.1
