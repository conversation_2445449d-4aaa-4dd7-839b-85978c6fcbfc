<idea-plugin package="cc.unitmesh" xmlns:xi="http://www.w3.org/2001/XInclude" allow-bundled-update="true">
    <id>cc.unitmesh.sketch</id>
    <name>AutoDev Sketch - Open Source Cursor-Like in IDEA with DeepSeek</name>
    <vendor>Phodal Huang</vendor>
    <description>AutoDev Sketch is an open-source AI coding assistant integrated into JetBrains IntelliJ IDEA, designed to enhance developer productivity.</description>

    <change-notes><![CDATA[
        todo
    ]]>
    </change-notes>

    <!-- please see https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html
         on how to target different products -->

    <xi:include href="/META-INF/autodev-core.xml" xpointer="xpointer(/idea-plugin/*)"/>

    <!--suppress PluginXmlValidity -->
    <content>
        <!-- language modules -->
        <module name="cc.unitmesh.pycharm"/>
        <module name="cc.unitmesh.idea"/>
        <module name="cc.unitmesh.kotlin"/>
        <module name="cc.unitmesh.javascript"/>
        <module name="cc.unitmesh.go"/>
        <module name="cc.unitmesh.rust"/>
<!--        <module name="cc.unitmesh.csharp"/>-->

        <!-- extension modules -->
        <module name="cc.unitmesh.database"/>
        <module name="cc.unitmesh.terminal"/>
        <module name="cc.unitmesh.mermaid"/>
        <module name="cc.unitmesh.plantuml"/>
        <module name="cc.unitmesh.git"/>
        <module name="cc.unitmesh.devti.language"/>
        <module name="cc.unitmesh.httpclient"/>
        <module name="cc.unitmesh.endpoints"/>
        <module name="cc.unitmesh.vue"/>
        <module name="cc.unitmesh.dependencies"/>
        <module name="cc.unitmesh.container"/>
    </content>
</idea-plugin>
