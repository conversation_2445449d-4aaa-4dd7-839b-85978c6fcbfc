<idea-plugin package="cc.unitmesh.sketch.language">
    <resource-bundle>messages.DevInBundle</resource-bundle>

    <dependencies>
        <plugin id="org.intellij.plugins.markdown"/>
        <plugin id="com.jetbrains.sh"/>
    </dependencies>

    <extensions defaultExtensionNs="com.intellij">
        <backgroundPostStartupActivity implementation="cc.unitmesh.sketch.language.startup.ShireActionStartupActivity"/>
        <vfs.asyncListener implementation="cc.unitmesh.sketch.language.startup.AsyncShireFileListener"/>
        <fileDocumentManagerListener implementation="cc.unitmesh.sketch.language.startup.ShireFileModificationListener"/>
        <editorFactoryDocumentListener implementation="cc.unitmesh.sketch.language.startup.ShireFileModificationListener"/>
        <copyPastePreProcessor implementation="cc.unitmesh.sketch.language.actions.copyPaste.ShireCopyPastePreProcessor"/>

        <!--        refs: https://github.com/JetBrains/intellij-sdk-code-samples/blob/main/simple_language_plugin/src/main/resources/META-INF/plugin.xml-->
        <fileType name="DevInFile" implementationClass="cc.unitmesh.sketch.language.DevInFileType" fieldName="INSTANCE"
                  language="DevIn" extensions="devin"/>

        <fileBasedIndex implementation="cc.unitmesh.sketch.language.index.DevInIdentifierIndex"/>

        <lang.parserDefinition language="DevIn"
                               implementationClass="cc.unitmesh.sketch.language.parser.DevInParserDefinition"/>
        <lang.syntaxHighlighterFactory language="DevIn"
                                       implementationClass="cc.unitmesh.sketch.language.highlight.DevInSyntaxHighlighterFactory"/>

        <annotator language="DevIn" implementationClass="cc.unitmesh.sketch.language.highlight.DevInHighlightingAnnotator"/>

        <lang.ast.factory language="DevIn"
                          implementationClass="cc.unitmesh.sketch.language.DevInAstFactory"/>

        <typedHandler implementation="cc.unitmesh.sketch.language.DevInTypedHandler"/>

        <gotoDeclarationHandler implementation="cc.unitmesh.sketch.language.navigation.ShireGotoDeclarationHandler"/>

        <completion.contributor language="DevIn"
                                id="devInCompletionContributor"
                                order="last"
                                implementationClass="cc.unitmesh.sketch.language.completion.DevInCompletionContributor"/>
        <completion.contributor language="DevIn"
                                order="last"
                                implementationClass="cc.unitmesh.sketch.language.completion.UserCustomCompletionContributor"/>

        <lang.foldingBuilder language="DevIn"
                             implementationClass="cc.unitmesh.sketch.language.folding.DevInCustomVariableFoldingBuilder"/>
        <lang.foldingBuilder language="DevIn"
                             implementationClass="cc.unitmesh.sketch.language.folding.DevInFileReferenceFoldingBuilder"/>

        <languageInjector implementation="cc.unitmesh.sketch.language.DevInLanguageInjector"/>


        <configurationType implementation="cc.unitmesh.sketch.language.run.DevInsConfigurationType"/>
        <programRunner implementation="cc.unitmesh.sketch.language.run.DevInsProgramRunner"/>
        <runConfigurationBeforeRunProviderDelegate
                implementation="cc.unitmesh.sketch.language.run.DevInsBeforeRunProviderDelegate"/>
        <runConfigurationProducer implementation="cc.unitmesh.sketch.language.run.DevInsRunConfigurationProducer"/>
        <runLineMarkerContributor language="DevIn"
                                  implementationClass="cc.unitmesh.sketch.language.run.DevInsRunLineMarkersProvider"/>

        <!--   Debugger  -->
        <programRunner implementation="cc.unitmesh.sketch.language.debugger.ShireDebugRunner"/>
        <xdebugger.breakpointType implementation="cc.unitmesh.sketch.language.debugger.ShireLineBreakpointType"/>
        <xdebugger.settings implementation="cc.unitmesh.sketch.language.debugger.ShireDebugSettings"/>
        <fileEditorProvider implementation="cc.unitmesh.sketch.language.debugger.editor.ShireSplitEditorProvider"/>

        <lang.commenter language="DevIn" implementationClass="cc.unitmesh.sketch.language.commenter.DevInsCommenter"/>

        <lang.foldingBuilder language="DevIn"
                             implementationClass="cc.unitmesh.sketch.language.folding.DevInFoldingBuilder"/>
        <lang.documentationProvider language="DevIn"
                                    id="devinsDocumentationProvider"
                                    implementationClass="cc.unitmesh.sketch.language.documentation.DevInsDocumentationProvider"/>

        <localInspection language="DevIn" groupPath="DevIn" groupName="Lints"
                         displayName="Duplicate agent declaration"
                         enabledByDefault="true"
                         level="ERROR"
                         implementationClass="cc.unitmesh.sketch.language.lints.DevInsDuplicateAgentInspection"/>

        <intentionAction>
            <className>cc.unitmesh.sketch.language.actions.intention.AutoDevIntentionHelper</className>
            <categoryKey>autodev.intention.category</categoryKey>
        </intentionAction>
    </extensions>

    <actions>
        <action id="runDevInsFileAction"
                class="cc.unitmesh.sketch.language.actions.DevInsRunFileAction"
                use-shortcut-of="RunClass"/>

        <!-- Shire Context Action Group -->
        <group id="AutoDevContextActionGroup"
               popup="true" text="AutoDev DevIns Action" description="AutoDev DevIns context action group"
               class="cc.unitmesh.sketch.language.actions.context.AutoDevContextMenuActionGroup"
               icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT" searchable="false">

            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
        </group>

        <!-- When multiple commit menu -->
        <group id="AutoDevVcsActionGroup"
               class="cc.unitmesh.sketch.language.actions.vcs.AutoDevVcsActionGroup"
               icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT"
               description="AutoDev VCS Action">

            <add-to-group group-id="Vcs.MessageActionGroup"/>
        </group>

        <!-- When one commit menu -->
        <action id="AutoDevCommitMessage"
                class="cc.unitmesh.sketch.language.actions.vcs.AutoDevVcsSingleAction"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT"
                text="Commit Message Action"
                description="AutoDev VCS Action">

            <add-to-group group-id="Vcs.MessageActionGroup"/>
        </action>

        <action id="AutoDevCustomInputBox"
                class="cc.unitmesh.sketch.language.actions.input.AutoDevInputBoxAction"
                description="You can custom any assistant as you want!"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT"
        >
            <keyboard-shortcut keymap="$default" first-keystroke="control BACK_SLASH"/>
        </action>

        <action id="AutoDevTerminalAction"
                class="cc.unitmesh.sketch.language.actions.terminal.AutoDevTerminalAction"
                description="You can custom any assistant as you want!"
                text="Terminal Action"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT"
        >
        </action>

        <action id="AutoDevConsoleAction"
                class="cc.unitmesh.sketch.language.actions.console.AutoDevConsoleAction"
                description="Ask AI fix this code"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT"
        >
            <add-to-group group-id="ConsoleEditorPopupMenu" anchor="first"/>
        </action>

        <!-- Shire Intentions Action Group -->
<!--        <group id="AutoDevIntentionsActionGroup" class="cc.unitmesh.sketch.language.actions.intention.AutoDevIntentionsActionGroup"-->
<!--               icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT" searchable="false">-->

<!--            <add-to-group group-id="ShowIntentionsGroup" relative-to-action="ShowIntentionActions" anchor="after"/>-->
<!--            <add-to-group group-id="Floating.CodeToolbar" anchor="first"/>-->
<!--        </group>-->

        <action id="AutoDevDatabaseAction"
                class="cc.unitmesh.sketch.language.actions.database.AutoDevDatabaseAction"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT"
                text="Shire Database Action"
                description="Shire database action">

        </action>

        <action id="AutoDevSonarLintAction"
                class="cc.unitmesh.sketch.language.actions.external.AutoDevSonarLintAction"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT"
                text="Shire SonarLint Action"
                description="Shire SonarLint action">

        </action>

        <action id="AutoDevVcsLogAction"
                class="cc.unitmesh.sketch.language.actions.vcs.AutoDevVcsLogAction"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT"
                text="Shire Vcs Action"
                description="Shire vcs action">

        </action>
    </actions>

    <extensions defaultExtensionNs="cc.unitmesh">
        <languageProcessor implementation="cc.unitmesh.sketch.language.provider.DevInsPromptProcessor"/>

        <runService implementation="cc.unitmesh.sketch.language.compiler.service.ShellRunService"/>
        <runService implementation="cc.unitmesh.sketch.language.compiler.service.DevInRunService"/>

        <sketchToolchainProvider implementation="cc.unitmesh.sketch.language.compiler.DevInsSketchToolchainProvider"/>

        <devInsAgentTool implementation="cc.unitmesh.sketch.language.startup.DevInsAgentToolchainProviderCollector"/>

        <!--        Shire -->
<!--        <shireTerminalExecutor implementation="cc.unitmesh.sketch.language.provider.ShireTerminalExecutor"/>-->
        <!-- code processors -->
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.TimeMetricProcessor"/>

        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.VerifyCodeProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.ParseCodeProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.RunCodeProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.InsertCodeProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.FormatCodeProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.PatchProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.DiffProcessor"/>

        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.AppendProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.InsertNewlineProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.UpdateEditorTextProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.ParseCommentProcessor"/>

        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.SaveFileProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.OpenFileProcessor"/>

        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.OpenWebpageProcessor"/>
        <shirePostProcessor implementation="cc.unitmesh.sketch.language.middleware.builtin.ShowWebviewProcessor"/>

        <!--   EditorInteractionProvider-->
        <shireLocationInteraction implementation="cc.unitmesh.sketch.language.task.EditorInteractionProvider"/>
    </extensions>
</idea-plugin>