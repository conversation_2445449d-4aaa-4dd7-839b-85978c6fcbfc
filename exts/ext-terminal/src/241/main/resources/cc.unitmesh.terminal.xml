<idea-plugin package="cc.unitmesh.terminal">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="org.jetbrains.plugins.terminal"/>
    </dependencies>

    <actions>
        <action id="ShellSuggestionAction"
                class="cc.unitmesh.terminal.ShellCommandSuggestAction"
                description="Suggestions for shell commands"
                text="Shell Command Suggestions"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT">

            <add-to-group group-id="TerminalToolwindowActionGroup" anchor="last"/>
        </action>

        <action id="terminal.FixAction"
                class="cc.unitmesh.sketch.actions.console.FixThisAction"
                description="Ask AI fix this code"
                icon="cc.unitmesh.sketch.AutoDevIcons.AI_COPILOT">

            <add-to-group group-id="Terminal.PromptContextMenu" anchor="first"/>
            <add-to-group group-id="Terminal.OutputContextMenu" anchor="before" relative-to-action="Terminal.CopyBlock"/>
        </action>
    </actions>

    <extensions defaultExtensionNs="cc.unitmesh">
        <langSketchProvider implementation="cc.unitmesh.terminal.sketch.TerminalSketchProvider"/>
    </extensions>
</idea-plugin>
