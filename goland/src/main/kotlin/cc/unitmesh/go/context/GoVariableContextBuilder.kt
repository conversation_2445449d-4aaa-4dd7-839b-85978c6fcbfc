package cc.unitmesh.go.context

import cc.unitmesh.sketch.context.VariableContext
import cc.unitmesh.sketch.context.builder.VariableContextBuilder
import com.goide.psi.GoVarOrConstDefinition
import com.intellij.psi.PsiElement

class GoVariableContextBuilder : VariableContextBuilder {
    override fun getVariableContext(
        psiElement: PsiElement,
        withMethodContext: <PERSON>olean,
        withClassContext: <PERSON>ole<PERSON>,
        gatherUsages: Boolean
    ): VariableContext? {
        if (psiElement !is GoVarOrConstDefinition) {
            return null
        }

        val name = psiElement.name

        return VariableContext(
            psiElement, psiElement.text, name, null, null, emptyList(), false, false
        )
    }
}
