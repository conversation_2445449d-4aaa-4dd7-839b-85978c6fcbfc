name=AutoDevZh

autodev.chat=èå¤©

chat.panel.send=åé
chat.panel.newSketch=æ°å»º Sketch
chat.panel.initial.text='Enter' åéï¼'Shift+Enter' å¼å¯æ°è¡, '/' è°ç¨ DevIns å½ä»¤ï¼'@' è°ç¨ Agent
chat.panel.initial.text.noAgent='Enter' åéï¼'Shift+Enter' å¼å¯æ°è¡, '/' è°ç¨ DevIns å½ä»¤
chat.too.long.user.message=æ¶æ¯é¿åº¦å¤ªé¿ï¼åå«{0}ä¸ª Token
chat.input.tips=åå®¹ä¸è½ä¸ºç©º

intention.category.llm=AutoDev
intentions.write.action=çæä»£ç 
intentions.assistant.name=AutoDev AI èªå¨ Action
intentions.assistant.popup.title=AutoDev AI å©æ

intentions.chat.code.test.step.prepare-context=åå¤ä¸ä¸æä¸­
intentions.chat.code.test.step.collect-context=æ¶éæéæç¤ºè¯ä¸ä¸ä¸æ
intentions.chat.code.complete.name=ä»£ç è¡¥å¨
intentions.chat.code.complete.family.name=ä»£ç è¡¥å¨
intentions.chat.code.test.name=æµè¯æ­¤ä»£ç 
intentions.chat.code.test.family.name=æµè¯æ­¤ä»£ç 
intentions.chat.new.family.name=å¸¦ä»£ç çæ°èå¤©
intentions.chat.selected.code.name=åºäºæ­¤ä»£ç èå¤©
intentions.chat.selected.fragment.name=ä¸ {0} ä¸ªä»£ç æ®µèå¤©
intentions.chat.selected.element.name=ä¸ ''{0}'' {1} èå¤©
intentions.living.documentation.name=çæææ¡£
intentions.living.documentation.family.name=çæææ¡£
intentions.request.background.process.title=æ¨ç LLM æ­£å¨å¤çæ¨çè¯·æ±

autodev.custom.prompt.placeholder=å¨æ­¤å¤èªå®ä¹æç¤ºè¯
autodev.custom.intentions.family=èªå®ä¹æå¾

# è¯·å¿å é¤ä»¥ä¸è¡ï¼ä¹ä¸è¦éå½åå®ä»¬ï¼é¤éæ´æ¹ [LLMSettingCompoent] ç±»
settings.languageParam=è¯­è¨
settings.gitTypeParam=Git ç±»å
settings.gitLabUrlParam=Gitlab æå¡å¨ URL
settings.gitLabTokenParam=Gitlab ä»¤ç
settings.gitHubTokenParam=GitHub ä»¤ç
settings.maxTokenLengthParam=æå¤§ token é¿åº¦
settings.customEngineServerParam=LLM æå¡å¨ URL
settings.customModelParam=æ¨¡ååç§°
settings.customEngineTokenParam=LLM æå¡å¨å¯é¥
settings.delaySecondsParam=è¯·æ±å»¶è¿ç§æ°ï¼å¯éï¼
settings.customEngineResponseFormatParam=èªå®ä¹ååºæ ¼å¼ï¼Json è·¯å¾ï¼
settings.customEngineResponseTypeParam=èªå®ä¹ååºç±»å
settings.customEngineRequestBodyFormatParam=èªå®ä¹è¯·æ±ä¸»ä½æ ¼å¼ï¼Jsonï¼
settings.customEngineRequestHeaderFormatParam=èªå®ä¹è¯·æ±å¤´æ ¼å¼ï¼Jsonï¼

settings.customize.title=æºè½ä½èªå®ä¹
counit.agent.enable.label=å¯ç¨èªå®ä¹æºè½ä½ï¼å®éªæ§ï¼
counit.agent.json.placeholder=èªå®ä¹æºè½ä½ JSON éç½®

action.new.genius.cicd.github=çæ GitHub Actions

settings.external.team.prompts.path=å¢é AI è·¯å¾

settings.autodev.coder=é«çº§è®¾ç½®
settings.autodev.coder.recordingInLocal=æ¬å°è®°å½ AI è¯·æ±åååºï¼Instructionï¼
settings.autodev.coder.trimCodeBeforeSend=å¨åéä»£ç ä¹åä¿®åªä»£ç 
settings.autodev.coder.disableAdvanceContext=ç¦ç¨é«çº§ä¸ä¸æ
settings.autodev.coder.disableAdvanceContext.tips=å¦æ¡æ¶ä¸ä¸æãè¯­è¨ä¸ä¸æç­
settings.autodev.coder.inEditorCompletion=ç¼è¾å¨ä¸­çå®æ¶è¡¥å¨
settings.autodev.coder.noChatHistory=æ²¡æèå¤©è®°å½
settings.autodev.coder.enableExportAsMcpServer=åè®¸ä½ä¸º MCP æå¡å¨è¾åº
settings.autodev.coder.enableObserver=ä½¿ç¨ Observer æ¨¡å¼
settings.autodev.coder.customActions=èªå®ä¹æç¤ºè¯ Actionï¼Jsonï¼ï¼

settings.autodev.devops=DevOps (SDLC)

# ç¶ææ 
autodev.statusbar.name=AutoDev ç¶ææ 
autodev.statusbar.popup.title=AutoDev ç¶ææ 
autodev.statusbar.id=autodev.statusBarPopup

# PL/SQL
migration.database.plsql=PL/SQL è¿ç§»
migration.database.plsql.generate.function=çæå½æ°
migration.database.plsql.generate.unittest=çæååæµè¯
migration.database.plsql.generate.entity=çæå®ä½

# èªå¨ SQL
autosql.name=AutoSQL
autosql.generate=çæ SQL
autosql.generate.clarify=æ¾æ¸éæ±
autosql.generate.generate=çæ SQL

# èªå¨é¡µé¢
autopage.generate=åç«¯çæ
autopage.generate.name=AutoPage çæ
autopage.generate.clarify=æ¾æ¸éæ±
autopage.generate.design=è®¾è®¡é¡µé¢

# Inlay
intentions.chat.inlay.complete.name=è¡¥å¨ä»£ç (Inlay æ¨¡å¼)
intentions.chat.inlay.complete.family.name =è¡¥å¨ä»£ç (Inlay æ¨¡å¼)
progress.run.task=æ­£å¨è¿è¡ä»»å¡

# å³é®
settings.autodev.rightClick.explain=è§£éæ­¤å¤
settings.autodev.rightClick.refactor=éææ­¤å¤
settings.autodev.rightClick.fixThis=ä¿®å¤æ­¤å¤
settings.autodev.rightClick.chat=è®¨è®ºæ­¤å¤
settings.autodev.rightClick.genApiTest=çæ API æµè¯

# others
settings.autodev.others.fixThis=ä¿®å¤æ­¤é®é¢ (AutoDev)
settings.autodev.others.quickAssistant=ææ¬çæä»£ç  (AutoDev)
settings.autodev.others.commitMessage=æäº¤æ¶æ¯ (AutoDev)
settings.autodev.others.generateReleaseNote=çæåå¸è¯´æ (AutoDev)
settings.autodev.others.codeReview=ä»£ç å®¡æ¥ (AutoDev)
settings.autodev.others.codeComplete=è¡¥å¨ä»£ç  (AutoDev)
settings.autodev.others.editSettings=ç¼è¾è®¾ç½®

# simple prompts
prompts.autodev.explainCode=è§£éå¦ä¸ {0} ä»£ç ã
prompts.autodev.refactorCode=éæç»å®ç {0} ä»£ç ã
prompts.autodev.completeCode=å®æ {0} ä»£ç ï¼è¿åå©ä½ä»£ç ï¼ä¸è§£éã
prompts.autodev.generateTest=ä¸º {0} ä»£ç çææµè¯ã
prompts.autodev.fixProblem=å¸®æä¿®å¤é®é¢ï¼
prompts.autodev.generateReleaseNote=çæåå¸è¯´æ
prompts.autodev.generateTestData=åºäºç»å®ç {0} ä»£ç åè¯·æ±/ååºä¿¡æ¯çæ API æµè¯è¯·æ±ï¼ä½¿ç¨ markdown ä»£ç åï¼ãè¿æ ·æä»¬å°±å¯ä»¥ç¨å®æ¥æµè¯ API
settings.autodev.coder.enableRenameSuggestion=å¯ç¨éå½åå»ºè®®
settings.autodev.coder.enableAutoRepairDiff=å¯ç¨èªå¨ Apply åä¿®å¤ Diff
settings.autodev.coder.enableAutoRunTerminal=å¯ç¨èªå¨è¿è¡ç»ç«¯ï¼æé£é©ï¼
settings.autodev.coder.enableAutoLintCode=å¯ç¨èªå¨ä¿®å¤ Lint ä»£ç 
settings.autodev.coder.enableRenderWebview=å¨èå¤©é¡µä¸­å¯ç¨æ¸²æ WebView
settings.autodev.coder.enableAutoScrollInSketch=å¨ Sketch ä¸­å¯ç¨èªå¨æ»å¨
settings.autodev.coder.enableDiffViewer=å¨ Sketch ä¸­å¯ç¨ Diff è§å¾
shell.command.suggestion.action.default.text=å¦ä½åå»ºä¸ä¸ªæ°çåæ¯?
batch.nothing.to.testing=æ²¡æè¦ AutoTest çåå®¹
intentions.chat.code.test.verify=éªè¯æµè¯ä¸­
custom.agent.open.documents=æå¼ææ¡£
settings.autodev.coder.testConnectionButton.tips=è¯·è®°å¾å¨ä¿®æ¹åç¹å»åºç¨ï¼åè¿è¡æµè¯ï¼

sketch.patch.action.accept=Accept
sketch.patch.action.accept.tooltip=Accept the change
sketch.patch.action.reject=Reject
sketch.patch.action.reject.tooltip=Reject the change
sketch.patch.action.viewDiff.tooltip=æ¥çåæ´
sketch.patch.action.applyDiff.tooltip=éçº³ Diff
sketch.patch.action.repairDiff.tooltip=ä¿®å¤ Diffï¼ä½¿ç¨ FastApplyï¼
# rollback
prompts.autodev.inlineChat=According user selection to ask user question
prompts.autodev.sketch=Sketch
sketch.composer.mode=Composer Modeï¼èªå¨æ¨¡å¼ï¼
sketch.lint.error.tooltip=æ¥ç Lint/Inspection éè¯¯
sketch.lint.error=åç° {0} ä¸ª Lint/Inspection éè¯¯
custom.action=Custom Action
custom.living.documentation=Custom Living Documentation
sketch.dependencies.check=Check dependencies has Issues
prompts.autodev.bridge=Bridge
autodev.custom.llms.placeholder=èªå®ä¹ä¸å LLM
settings.autodev.coder.customLlms=èªå®ä¹ä¸åæ¨¡å: Plan/ACT/Completion/Embedding/FastApply
sketch.compile.devins=æ¶éä¸ä¸æä¸­ï¼å³è½¬è¯ AutoDev DevIns æä»¤ï¼
autodev.run.action=Run this file
llm.error.url.scheme=è¯·è¯·è¯·åéç½®å¥½æ¨çæ¨¡åï¼åèææ¡£ï¼https://ide.unitmesh.cc/quick-start
counit.mcp.services.placeholder=éç½® MCP Servers
sketch.plan.finish.task=è¯·å¸®æå®æä»¥ä¸ä»»å¡:
sketch.plan.review=AI éæ°è¯ä¼° Sketch è®¡å
sketch.plan.edit=æ·»å /ç¼è¾è®¡å
sketch.plan.reviewing=å®¡éè®¡åä¸­...
sketch.write.to.file=åå¥æä»¶
sketch.plan.empty=æåçè®¡åæ¯ç©ºç
settings.autodev.coder.requires.restart=éè¦éå¯ IDE
sketch.terminal.execute=æ§è¡ Shell
sketch.terminal.copy.text=å¤å¶æä»¶
sketch.terminal.send.chat=åéå°èå¤©
sketch.terminal.popup=å¼¹çª Terminal
autodev.insert.action=æ·»å å°è¾å¥æ¡ä¸­
autodev.copy.action=å¤å¶
autodev.save.action=ä¿å­æä»¶
sketch.patch.repaired=å·²ä¿®å¤
sketch.patch.repair=ä¿®å¤
sketch.patch.apply=éçº³
sketch.patch.view=æ¥ç
sketch.diff.original=åå§ä»£ç 
sketch.diff.aiSuggestion=AI å»ºè®®
sketch.patch.failed.read=è¯»åæä»¶å¤±è´¥: {0}
sketch.patch.failed.apply=åºç¨è¡¥ä¸å¤±è´¥: {0}
sketch.patch.document.null=æä»¶ {0} çææ¡£ä¸ºç©º
autodev.save.as.file=ä¿å­æä»¶
autodev.save.as.file.description=éæ©å¾ä¿å­æä»¶ä½ç½®
sketch.patch.regenerate=åçæ
sketch.patch.action.regenerate.tooltip=éæ°çæä»£ç 
sketch.terminal.show.hide=æ¾ç¤ºæéèç»ç«¯

chat.panel.stop=åæ­¢
chat.panel.enhance=ä¸°å¯æç¤ºè¯
chat.panel.clear.all=æ¸ç©º
chat.panel.clear.all.tooltip=æ¸é¤æææä»¶
chat.panel.select.files=è¯·åå»éæ©æä»¶ï¼ä»¥æ¾å°è¾å¥æ¡åï¼ç¸å³æä»¶ï¼
sketch.plan.create=åå»ºé®é¢ issue
planner.stats.changes.empty=æ²¡æåæ´
planner.change.list.title=åæ´åè¡¨
planner.action.discard.all=ä¸¢å¼å¨é¨
planner.action.accept.all=æ¥åå¨é¨
planner.stats.no.changes=æ²¡æåæ´
planner.no.code.changes=æ²¡æä»£ç åæ´
planner.stats.changes.count= (å± {0} ä¸ªæä»¶åæ´)
planner.action.view.changes=æ¥çåæ´
planner.action.discard.changes=ä¸¢å¼åæ´
planner.action.accept.changes=Accept
planner.error.no.file=æ²¡ææä»¶ {0}
planner.error.no.after.file=æªæ¾å°åæ´åçæä»¶è·¯å¾
planner.error.create.file=Create file error {0}
sketch.plan.rerun.task=Help me fix this failed task:
sketch.terminal.stop=åæ­¢ Terminal
sketch.mcp.testMcp=æµè¯ MCP æå¡
sketch.mcp.services.docs=MCP ææ¡£
sketch.issue.input.placeholder=è¯·è¾å¥éæ±ãé®é¢æè¿°
sketch.issue.input.submit=æäº¤
sketch.issue.input.cancel=åæ¶
planner.task.status.completed=å·²å®æ
planner.task.status.failed=å·²å¤±è´¥
planner.task.status.in_progress=è¿è¡ä¸­
planner.task.status.todo=å¾å
planner.task.execute=æ§è¡
chat.panel.workspace.files=å·¥ä½åºæä»¶
chat.panel.add.files.tooltip=æ·»å æä»¶å°å·¥ä½åº
chat.panel.select.files.title=éæ©å·¥ä½åºæä»¶
chat.panel.select.files.description=éæ©è¦æ·»å å°å·¥ä½åºçæä»¶
chat.panel.remove.file.tooltip=ä»å·¥ä½åºç§»é¤æä»¶
chat.panel.add.openFiles=æ·»å æææå¼çæä»¶

indexer.generate.domain=çæ domain.csv

# MCP Chat Config Dialog
mcp.chat.config.dialog.title=æ¨¡åéç½®
mcp.chat.config.dialog.temperature=æ¸©åº¦: {0}
mcp.chat.config.dialog.enabled.tools=å·²å¯ç¨å·¥å·
# MCP Chat Result Panel
mcp.chat.result.tab.response=ååº
mcp.chat.result.tab.tools=å·¥å·
mcp.chat.result.no.tools=ååºä¸­æªæ¾å°å·¥å·è°ç¨
mcp.chat.result.execute=æ§è¡
mcp.chat.result.executing=æ­£å¨æ§è¡å·¥å· {0}...
mcp.chat.result.error.tool.not.found=éè¯¯ï¼æ¾ä¸å°å¹éçå·¥å· ''{0}''
mcp.chat.result.execution.time=èæ¶
mcp.chat.result.execute.all=æ§è¡ææå·¥å·
mcp.chat.result.tab.messages=æ¶æ¯æ¥å¿
# MCP Tool Detail Dialog
mcp.tool.detail.dialog.title=MCP å·¥å·è¯¦æ - {0}
mcp.tool.detail.dialog.from.server=æ¥èªæå¡: {0}
mcp.tool.detail.dialog.no.description=ææ æè¿°
mcp.tool.detail.dialog.parameters=åæ°
mcp.tool.detail.dialog.verify=éªè¯ï¼èªå¨çæï¼
mcp.tool.detail.dialog.result=ç»æ
mcp.tool.detail.dialog.execute=æ§è¡
# McpPreviewEditor
mcp.preview.editor.title=MCP å·¥å·
mcp.preview.editor.search.placeholder=æç´¢å·¥å·...
mcp.preview.editor.model.label=æ¨¡å
mcp.preview.editor.configure.button=éç½®
mcp.preview.editor.test.button.tooltip=æµè¯è°ç¨å·¥å·
mcp.preview.editor.empty.message.warning=è¯·è¾å¥è¦åéçæ¶æ¯
mcp.preview.editor.loading.response=å è½½ååºä¸­...
# McpFileEditorWithPreview
mcp.editor.preview.title=é¢è§
mcp.editor.preview.tooltip=é¢è§é¢æ¿
mcp.editor.refresh.title=å·æ°
shire.toolchain.function.not.found=Toolchain Not Found {0}
shire.run.local.mode=Run Local Model
devins.llm.notfound=LLM not found
devins.llm.done=Done
intentions.step.prepare-context=Prepare Context
devins.intention=Shire intention action
devins.newFile=New File
devins.file=DevIns File
action.view.history.text=View History
action.view.history.description=View conversation history
popup.title.session.history=History
